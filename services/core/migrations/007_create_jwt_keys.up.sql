-- Create JWT keys table for RSA key management
CREATE TABLE IF NOT EXISTS jwt_keys (
    id VARCHAR(255) PRIMARY KEY,
    algorithm VARCHAR(10) NOT NULL DEFAULT 'RS256',
    use_type VARCHAR(10) NOT NULL DEFAULT 'sig',
    key_type VARCHAR(10) NOT NULL DEFAULT 'RSA',
    private_key_pem TEXT NOT NULL,
    n TEXT NOT NULL, -- RSA modulus (base64url encoded)
    e TEXT NOT NULL, -- RSA exponent (base64url encoded)
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT FALSE,
    
    CONSTRAINT valid_algorithm CHECK (algorithm IN ('RS256')),
    CONSTRAINT valid_use_type CHECK (use_type IN ('sig')),
    CONSTRAINT valid_key_type CHECK (key_type IN ('RSA'))
);

-- Create indexes for efficient queries
CREATE INDEX idx_jwt_keys_active ON jwt_keys(is_active) WHERE is_active = true;
CREATE INDEX idx_jwt_keys_expires_at ON jwt_keys(expires_at);
CREATE INDEX idx_jwt_keys_created_at ON jwt_keys(created_at);

-- Ensure only one active key at a time (partial unique index)
CREATE UNIQUE INDEX idx_jwt_keys_single_active ON jwt_keys(is_active) WHERE is_active = true;

-- Add comments for documentation
COMMENT ON TABLE jwt_keys IS 'Stores RSA key pairs for JWT signing and verification';
COMMENT ON COLUMN jwt_keys.id IS 'Unique key identifier used in JWT kid header';
COMMENT ON COLUMN jwt_keys.algorithm IS 'JWT signing algorithm (RS256)';
COMMENT ON COLUMN jwt_keys.use_type IS 'Key usage type (sig for signature)';
COMMENT ON COLUMN jwt_keys.key_type IS 'Key type (RSA)';
COMMENT ON COLUMN jwt_keys.private_key_pem IS 'RSA private key in PEM format';
COMMENT ON COLUMN jwt_keys.n IS 'RSA modulus for JWKS endpoint (base64url encoded)';
COMMENT ON COLUMN jwt_keys.e IS 'RSA exponent for JWKS endpoint (base64url encoded)';
COMMENT ON COLUMN jwt_keys.is_active IS 'Whether this key is currently used for signing';
