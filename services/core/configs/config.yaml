server:
  name: core-service
  port: 8080
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 120s
  shutdown_timeout: 30s

database:
  host: localhost
  port: 5432
  name: mytorra_core
  user: mytorra
  password: ${DB_PASSWORD}
  max_connections: 25
  max_idle_connections: 5
  connection_lifetime: 5m
  ssl_mode: disable

redis:
  host: localhost
  port: 6379
  password: ${REDIS_PASSWORD}
  db: 0
  pool_size: 10
  min_idle_conns: 5
  max_retries: 3
  read_timeout: 3s
  write_timeout: 3s

rabbitmq:
  url: amqp://guest:guest@localhost:5672/
  exchange: mytorra.events
  exchange_type: topic
  queue_prefix: core-service

jwt:
  secret: ${JWT_SECRET}
  access_duration: 15m
  refresh_duration: 168h  # 7 days
  issuer: mytorra-core
  # RSA Key Management for Zero-Trust Authentication
  key_size: 2048
  key_validity: 720h  # 30 days
  rotation_period: 24h

stripe:
  secret_key: ${STRIPE_SECRET_KEY}
  webhook_secret: ${STRIPE_WEBHOOK_SECRET}

sendgrid:
  api_key: ${SENDGRID_API_KEY}
  from_email: <EMAIL>
  from_name: MyTorra

fcm:
  project_id: ${FCM_PROJECT_ID}
  credentials_file: ${FCM_CREDENTIALS_FILE}

log:
  level: info
  format: json

metrics:
  enabled: true
  path: /metrics
  port: 9090

rate_limit:
  requests_per_second: 10
  burst: 20
