package valueobjects

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// JWTKeyPair represents an RSA key pair for JWT signing
type JW<PERSON><PERSON>eyPair struct {
	ID         string    `json:"id"`         // Key ID for JWKS
	Algorithm  string    `json:"alg"`        // Always "RS256"
	Use        string    `json:"use"`        // Always "sig" for signature
	KeyType    string    `json:"kty"`        // Always "RSA"
	PrivateKey *rsa.PrivateKey `json:"-"`    // Never serialize private key
	PublicKey  *rsa.PublicKey  `json:"-"`    // Public key for verification
	N          string    `json:"n"`          // RSA modulus (base64url encoded)
	E          string    `json:"e"`          // RSA exponent (base64url encoded)
	CreatedAt  time.Time `json:"created_at"`
	ExpiresAt  time.Time `json:"expires_at"`
	IsActive   bool      `json:"is_active"`
}

// NewJWTKeyPair generates a new RSA key pair for JWT signing
func NewJWTKeyPair(keySize int, validityDuration time.Duration) (*JWTKeyPair, error) {
	if keySize < 2048 {
		return nil, fmt.Errorf("key size must be at least 2048 bits")
	}

	// Generate RSA private key
	privateKey, err := rsa.GenerateKey(rand.Reader, keySize)
	if err != nil {
		return nil, fmt.Errorf("failed to generate RSA key: %w", err)
	}

	publicKey := &privateKey.PublicKey

	// Generate unique key ID
	keyID := uuid.New().String()

	// Encode public key components for JWKS
	nBytes := publicKey.N.Bytes()
	eBytes := make([]byte, 8)
	for i := 0; i < len(eBytes); i++ {
		if i < 8-len(publicKey.E.Bytes()) {
			eBytes[i] = 0
		} else {
			eBytes[i] = publicKey.E.Bytes()[i-(8-len(publicKey.E.Bytes()))]
		}
	}

	// Base64url encode (without padding)
	n := base64.RawURLEncoding.EncodeToString(nBytes)
	e := base64.RawURLEncoding.EncodeToString(publicKey.E.Bytes())

	now := time.Now()
	return &JWTKeyPair{
		ID:         keyID,
		Algorithm:  "RS256",
		Use:        "sig",
		KeyType:    "RSA",
		PrivateKey: privateKey,
		PublicKey:  publicKey,
		N:          n,
		E:          e,
		CreatedAt:  now,
		ExpiresAt:  now.Add(validityDuration),
		IsActive:   true,
	}, nil
}

// ToJWK returns the public key in JSON Web Key format for JWKS endpoint
func (k *JWTKeyPair) ToJWK() map[string]interface{} {
	return map[string]interface{}{
		"kty": k.KeyType,
		"use": k.Use,
		"alg": k.Algorithm,
		"kid": k.ID,
		"n":   k.N,
		"e":   k.E,
	}
}

// IsExpired checks if the key pair has expired
func (k *JWTKeyPair) IsExpired() bool {
	return time.Now().After(k.ExpiresAt)
}

// ShouldRotate checks if the key should be rotated (e.g., 80% of lifetime passed)
func (k *JWTKeyPair) ShouldRotate() bool {
	lifetime := k.ExpiresAt.Sub(k.CreatedAt)
	rotationThreshold := k.CreatedAt.Add(time.Duration(float64(lifetime) * 0.8))
	return time.Now().After(rotationThreshold)
}

// ToPEM exports the private key in PEM format for storage
func (k *JWTKeyPair) ToPEM() ([]byte, error) {
	privateKeyBytes, err := x509.MarshalPKCS8PrivateKey(k.PrivateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal private key: %w", err)
	}

	privateKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PRIVATE KEY",
		Bytes: privateKeyBytes,
	})

	return privateKeyPEM, nil
}

// FromPEM loads a private key from PEM format
func (k *JWTKeyPair) FromPEM(pemData []byte) error {
	block, _ := pem.Decode(pemData)
	if block == nil {
		return fmt.Errorf("failed to decode PEM block")
	}

	privateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return fmt.Errorf("failed to parse private key: %w", err)
	}

	rsaPrivateKey, ok := privateKey.(*rsa.PrivateKey)
	if !ok {
		return fmt.Errorf("key is not an RSA private key")
	}

	k.PrivateKey = rsaPrivateKey
	k.PublicKey = &rsaPrivateKey.PublicKey

	// Re-encode public key components
	nBytes := k.PublicKey.N.Bytes()
	k.N = base64.RawURLEncoding.EncodeToString(nBytes)
	k.E = base64.RawURLEncoding.EncodeToString(k.PublicKey.E.Bytes())

	return nil
}

// Validate ensures the key pair is valid and secure
func (k *JWTKeyPair) Validate() error {
	if k.PrivateKey == nil {
		return fmt.Errorf("private key is nil")
	}

	if k.PublicKey == nil {
		return fmt.Errorf("public key is nil")
	}

	// Check key size (minimum 2048 bits for security)
	keySize := k.PrivateKey.N.BitLen()
	if keySize < 2048 {
		return fmt.Errorf("key size %d is too small, minimum 2048 bits required", keySize)
	}

	if k.Algorithm != "RS256" {
		return fmt.Errorf("unsupported algorithm: %s", k.Algorithm)
	}

	if k.Use != "sig" {
		return fmt.Errorf("invalid key use: %s", k.Use)
	}

	if k.KeyType != "RSA" {
		return fmt.Errorf("invalid key type: %s", k.KeyType)
	}

	if k.ID == "" {
		return fmt.Errorf("key ID cannot be empty")
	}

	return nil
}
