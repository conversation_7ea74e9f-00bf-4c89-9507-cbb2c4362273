package services

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/paradoxe35/torra/packages/errors"
	"github.com/paradoxe35/torra/packages/i18n"
	"github.com/paradoxe35/torra/packages/logger"
	"github.com/paradoxe35/torra/packages/security"
	"github.com/paradoxe35/torra/services/core/internal/application/dtos"
	"github.com/paradoxe35/torra/services/core/internal/application/ports"
	"github.com/paradoxe35/torra/services/core/internal/domain/entities"
)

// MFAService handles Multi-Factor Authentication operations
type MFAService struct {
	mfaRepo    ports.MFARepository
	userRepo   ports.UserRepository
	cache      ports.Cache
	logger     logger.Interface
	translator *i18n.Translator
	mfaEngine  *security.MFAService
}

// NewMFAService creates a new MFA service
func NewMFAService(
	mfaRepo ports.MFARepository,
	userRepo ports.UserRepository,
	cache ports.Cache,
	logger logger.Interface,
	translator *i18n.Translator,
	issuer string,
) *MFAService {
	mfaEngine := security.NewMFAService(security.MFAConfig{
		Issuer:     issuer,
		CodeLength: 6,
		WindowSize: 1,
		Period:     30,
	})

	return &MFAService{
		mfaRepo:    mfaRepo,
		userRepo:   userRepo,
		cache:      cache,
		logger:     logger,
		translator: translator,
		mfaEngine:  mfaEngine,
	}
}

// SetupMFA initiates MFA setup for a user
func (s *MFAService) SetupMFA(ctx context.Context, userID uuid.UUID, req dtos.MFASetupRequest) (*dtos.MFASetupResponse, error) {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Check if method is already set up
	devices, err := s.mfaRepo.GetActiveDevicesByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get MFA devices: %w", err)
	}

	for _, device := range devices {
		if device.Method == req.Method {
			return nil, errors.ErrMFAAlreadyEnabled
		}
	}

	deviceID := uuid.New()
	response := &dtos.MFASetupResponse{
		DeviceID: deviceID.String(),
		Method:   string(req.Method),
	}

	switch req.Method {
	case entities.MFAMethodTOTP:
		return s.setupTOTP(ctx, user, deviceID, req.Name, response)
	case entities.MFAMethodSMS:
		return s.setupSMS(ctx, user, deviceID, req.Name, req.Phone, response)
	case entities.MFAMethodEmail:
		return s.setupEmail(ctx, user, deviceID, req.Name, req.Email, response)
	default:
		return nil, errors.ErrInvalidMFAMethod
	}
}

// setupTOTP sets up TOTP MFA
func (s *MFAService) setupTOTP(ctx context.Context, user *entities.User, deviceID uuid.UUID, name string, response *dtos.MFASetupResponse) (*dtos.MFASetupResponse, error) {
	// Generate TOTP secret
	secret, err := s.mfaEngine.GenerateTOTPSecret(user.Email.String())
	if err != nil {
		return nil, fmt.Errorf("failed to generate TOTP secret: %w", err)
	}

	// Encrypt the secret before storing
	encryptedSecret, err := security.Encrypt(secret.Secret, []byte("your-encryption-key")) // TODO: Use proper key management
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt secret: %w", err)
	}

	// Create MFA device (inactive until verified)
	device := &entities.MFADevice{
		ID:        deviceID,
		UserID:    user.ID,
		Method:    entities.MFAMethodTOTP,
		Secret:    encryptedSecret,
		Name:      name,
		IsActive:  false,                                      // Will be activated after verification
		IsPrimary: len(s.getActiveDevices(ctx, user.ID)) == 0, // First device is primary
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.mfaRepo.CreateDevice(ctx, device); err != nil {
		return nil, fmt.Errorf("failed to create MFA device: %w", err)
	}

	response.Secret = secret.Secret
	response.QRCodeURL = secret.QRCodeURL
	response.Message = s.translator.Get("mfa.totp.setup_instructions")

	return response, nil
}

// setupSMS sets up SMS MFA
func (s *MFAService) setupSMS(ctx context.Context, user *entities.User, deviceID uuid.UUID, name, phone string, response *dtos.MFASetupResponse) (*dtos.MFASetupResponse, error) {
	if phone == "" {
		return nil, errors.ErrInvalidPhoneNumber
	}

	// Encrypt the phone number
	encryptedPhone, err := security.Encrypt(phone, []byte("your-encryption-key")) // TODO: Use proper key management
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt phone: %w", err)
	}

	// Create MFA device (inactive until verified)
	device := &entities.MFADevice{
		ID:        deviceID,
		UserID:    user.ID,
		Method:    entities.MFAMethodSMS,
		Secret:    encryptedPhone,
		Name:      name,
		IsActive:  false,
		IsPrimary: len(s.getActiveDevices(ctx, user.ID)) == 0,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.mfaRepo.CreateDevice(ctx, device); err != nil {
		return nil, fmt.Errorf("failed to create MFA device: %w", err)
	}

	// Send verification code
	code, err := s.mfaEngine.GenerateSMSCode()
	if err != nil {
		return nil, fmt.Errorf("failed to generate SMS code: %w", err)
	}

	// Store code in cache for verification (5 minutes)
	cacheKey := fmt.Sprintf("mfa_setup_code:%s", deviceID.String())
	if err := s.cache.Set(ctx, cacheKey, []byte(code), 5*time.Minute); err != nil {
		s.logger.WithError(err).Error("Failed to cache MFA setup code")
	}

	// TODO: Send SMS using SMS service
	s.logger.Info(fmt.Sprintf("SMS code for MFA setup: %s (to %s)", code, phone))

	response.Message = s.translator.Get("mfa.sms.code_sent")
	return response, nil
}

// setupEmail sets up Email MFA
func (s *MFAService) setupEmail(ctx context.Context, user *entities.User, deviceID uuid.UUID, name, email string, response *dtos.MFASetupResponse) (*dtos.MFASetupResponse, error) {
	if email == "" {
		email = user.Email.String() // Use user's email if not provided
	}

	// Encrypt the email
	encryptedEmail, err := security.Encrypt(email, []byte("your-encryption-key")) // TODO: Use proper key management
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt email: %w", err)
	}

	// Create MFA device (inactive until verified)
	device := &entities.MFADevice{
		ID:        deviceID,
		UserID:    user.ID,
		Method:    entities.MFAMethodEmail,
		Secret:    encryptedEmail,
		Name:      name,
		IsActive:  false,
		IsPrimary: len(s.getActiveDevices(ctx, user.ID)) == 0,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.mfaRepo.CreateDevice(ctx, device); err != nil {
		return nil, fmt.Errorf("failed to create MFA device: %w", err)
	}

	// Send verification code
	code, err := s.mfaEngine.GenerateEmailCode()
	if err != nil {
		return nil, fmt.Errorf("failed to generate email code: %w", err)
	}

	// Store code in cache for verification (5 minutes)
	cacheKey := fmt.Sprintf("mfa_setup_code:%s", deviceID.String())
	if err := s.cache.Set(ctx, cacheKey, []byte(code), 5*time.Minute); err != nil {
		s.logger.WithError(err).Error("Failed to cache MFA setup code")
	}

	// TODO: Send email using email service
	s.logger.Info(fmt.Sprintf("Email code for MFA setup: %s (to %s)", code, email))

	response.Message = s.translator.Get("mfa.email.code_sent")
	return response, nil
}

// VerifySetup verifies MFA setup with a code
func (s *MFAService) VerifySetup(ctx context.Context, userID uuid.UUID, req dtos.MFAVerifySetupRequest) error {
	deviceUUID, err := uuid.Parse(req.DeviceID)
	if err != nil {
		return errors.ErrInvalidDeviceID
	}

	// Get the device
	device, err := s.mfaRepo.GetDeviceByID(ctx, deviceUUID)
	if err != nil {
		return fmt.Errorf("failed to get MFA device: %w", err)
	}

	if device == nil || device.UserID != userID {
		return errors.ErrDeviceNotFound
	}

	if device.IsActive {
		return errors.ErrMFAAlreadyEnabled
	}

	var valid bool
	switch device.Method {
	case entities.MFAMethodTOTP:
		valid, err = s.verifyTOTPSetup(ctx, device, req.Code)
	case entities.MFAMethodSMS, entities.MFAMethodEmail:
		valid, err = s.verifyCachedCode(ctx, device.ID.String(), req.Code)
	default:
		return errors.ErrInvalidMFAMethod
	}

	if err != nil {
		return fmt.Errorf("failed to verify setup code: %w", err)
	}

	if !valid {
		return errors.ErrInvalidMFACode
	}

	// Activate the device
	device.IsActive = true
	device.UpdatedAt = time.Now()
	if err := s.mfaRepo.UpdateDevice(ctx, device); err != nil {
		return fmt.Errorf("failed to activate MFA device: %w", err)
	}

	// Generate backup codes if this is the first MFA method
	devices, _ := s.mfaRepo.GetActiveDevicesByUserID(ctx, userID)
	if len(devices) == 1 { // This is the first active device
		if err := s.generateBackupCodes(ctx, userID); err != nil {
			s.logger.WithError(err).Error("Failed to generate backup codes")
		}
	}

	return nil
}

// Helper methods
func (s *MFAService) getActiveDevices(ctx context.Context, userID uuid.UUID) []*entities.MFADevice {
	devices, _ := s.mfaRepo.GetActiveDevicesByUserID(ctx, userID)
	return devices
}

func (s *MFAService) verifyTOTPSetup(ctx context.Context, device *entities.MFADevice, code string) (bool, error) {
	// Decrypt the secret
	secret, err := security.Decrypt(device.Secret, []byte("your-encryption-key")) // TODO: Use proper key management
	if err != nil {
		return false, fmt.Errorf("failed to decrypt secret: %w", err)
	}

	result, err := s.mfaEngine.VerifyTOTPCode(code, secret)
	if err != nil {
		return false, err
	}

	return result.Valid, nil
}

func (s *MFAService) verifyCachedCode(ctx context.Context, deviceID, code string) (bool, error) {
	cacheKey := fmt.Sprintf("mfa_setup_code:%s", deviceID)
	cachedCode, err := s.cache.Get(ctx, cacheKey)
	if err != nil {
		return false, fmt.Errorf("code expired or not found")
	}

	return string(cachedCode) == code, nil
}

func (s *MFAService) generateBackupCodes(ctx context.Context, userID uuid.UUID) error {
	codes, err := s.mfaEngine.GenerateBackupCodes(10)
	if err != nil {
		return err
	}

	backupCodes := make([]*entities.MFABackupCode, len(codes))
	for i, code := range codes {
		hashedCode, err := security.HashPassword(code)
		if err != nil {
			return err
		}

		backupCodes[i] = &entities.MFABackupCode{
			ID:        uuid.New(),
			UserID:    userID,
			Code:      hashedCode,
			IsUsed:    false,
			CreatedAt: time.Now(),
		}
	}

	return s.mfaRepo.CreateBackupCodes(ctx, backupCodes)
}

// GetMFAStatus returns the MFA status for a user
func (s *MFAService) GetMFAStatus(ctx context.Context, userID uuid.UUID) (*dtos.MFAStatusResponse, error) {
	// Get active devices
	devices, err := s.mfaRepo.GetActiveDevicesByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get MFA devices: %w", err)
	}

	// Get backup codes count
	backupCodes, err := s.mfaRepo.GetUnusedBackupCodesByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get backup codes: %w", err)
	}

	// Get primary device
	primaryDevice, err := s.mfaRepo.GetPrimaryDeviceByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get primary device: %w", err)
	}

	// Convert devices to response format
	deviceResponses := make([]dtos.MFADeviceResponse, len(devices))
	methods := make([]string, len(devices))
	for i, device := range devices {
		deviceResponses[i] = dtos.ToMFADeviceResponse(device)
		methods[i] = string(device.Method)
	}

	response := &dtos.MFAStatusResponse{
		Enabled:         len(devices) > 0,
		Methods:         methods,
		Devices:         deviceResponses,
		BackupCodesLeft: len(backupCodes),
	}

	if primaryDevice != nil {
		response.PrimaryMethod = string(primaryDevice.Method)
	}

	return response, nil
}

// DisableMFA disables all MFA for a user
func (s *MFAService) DisableMFA(ctx context.Context, userID uuid.UUID, req dtos.MFADisableRequest) error {
	// Get user to verify password
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Verify password
	if err := security.CheckPassword(req.Password, user.PasswordHash); err != nil {
		return fmt.Errorf("invalid password")
	}

	// If MFA code provided, verify it
	if req.Code != "" && user.MFASecret != nil {
		valid, err := s.verifyTOTPSetup(ctx, &entities.MFADevice{Secret: *user.MFASecret}, req.Code)
		if err != nil || !valid {
			return fmt.Errorf("invalid MFA code")
		}
	}

	// Deactivate all MFA devices
	devices, err := s.mfaRepo.GetActiveDevicesByUserID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get MFA devices: %w", err)
	}

	for _, device := range devices {
		if err := s.mfaRepo.DeactivateDevice(ctx, device.ID); err != nil {
			s.logger.WithError(err).Error("Failed to deactivate MFA device", "device_id", device.ID)
		}
	}

	// Delete backup codes
	if err := s.mfaRepo.DeleteBackupCodesByUserID(ctx, userID); err != nil {
		s.logger.WithError(err).Error("Failed to delete backup codes")
	}

	// Update user MFA status
	user.MFAEnabled = false
	user.MFASecret = nil
	if err := s.userRepo.Update(ctx, user); err != nil {
		s.logger.WithError(err).Error("Failed to update user MFA status")
	}

	return nil
}

// RegenerateBackupCodes generates new backup codes for a user
func (s *MFAService) RegenerateBackupCodes(ctx context.Context, userID uuid.UUID, req dtos.MFARegenerateBackupCodesRequest) (*dtos.MFARegenerateBackupCodesResponse, error) {
	// Get user to verify password
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Verify password
	if err := security.CheckPassword(req.Password, user.PasswordHash); err != nil {
		return nil, fmt.Errorf("invalid password")
	}

	// If MFA code provided, verify it
	if req.Code != "" && user.MFASecret != nil {
		if err := s.verifyTOTPSetup(ctx, &entities.MFADevice{Secret: *user.MFASecret}, req.Code); err != nil {
			return nil, fmt.Errorf("invalid MFA code")
		}
	}

	// Delete existing backup codes
	if err := s.mfaRepo.DeleteBackupCodesByUserID(ctx, userID); err != nil {
		return nil, fmt.Errorf("failed to delete existing backup codes: %w", err)
	}

	// Generate new backup codes
	if err := s.generateBackupCodes(ctx, userID); err != nil {
		return nil, fmt.Errorf("failed to generate backup codes: %w", err)
	}

	// Get the new codes (unhashed for display)
	codes, err := s.mfaEngine.GenerateBackupCodes(10)
	if err != nil {
		return nil, fmt.Errorf("failed to generate display codes: %w", err)
	}

	return &dtos.MFARegenerateBackupCodesResponse{
		BackupCodes: codes,
		Message:     "New backup codes generated successfully. Store them securely.",
	}, nil
}

// RemoveDevice removes an MFA device
func (s *MFAService) RemoveDevice(ctx context.Context, userID uuid.UUID, req dtos.MFARemoveDeviceRequest) error {
	deviceUUID, err := uuid.Parse(req.DeviceID)
	if err != nil {
		return fmt.Errorf("invalid device ID")
	}

	// Get user to verify password
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Verify password
	if err := security.CheckPassword(req.Password, user.PasswordHash); err != nil {
		return fmt.Errorf("invalid password")
	}

	// Get the device
	device, err := s.mfaRepo.GetDeviceByID(ctx, deviceUUID)
	if err != nil {
		return fmt.Errorf("failed to get device: %w", err)
	}

	if device == nil || device.UserID != userID {
		return fmt.Errorf("device not found")
	}

	// If MFA code provided, verify it
	if req.Code != "" {
		if err := s.verifyTOTPSetup(ctx, device, req.Code); err != nil {
			return fmt.Errorf("invalid MFA code")
		}
	}

	// Remove the device
	if err := s.mfaRepo.DeleteDevice(ctx, deviceUUID); err != nil {
		return fmt.Errorf("failed to remove device: %w", err)
	}

	return nil
}
