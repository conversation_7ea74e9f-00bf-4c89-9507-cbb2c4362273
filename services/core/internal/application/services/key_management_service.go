package services

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"sync"
	"time"

	"github.com/paradoxe35/torra/packages/logger"
	"github.com/paradoxe35/torra/services/core/internal/application/ports"
	"github.com/paradoxe35/torra/services/core/internal/domain/valueobjects"
)

// KeyManagementService handles RSA key pair generation, rotation, and management
type KeyManagementService struct {
	keyStore       ports.KeyStore
	cache          ports.Cache
	logger         logger.Interface
	keySize        int
	keyValidity    time.Duration
	rotationPeriod time.Duration
	currentKey     *valueobjects.JWTKeyPair
	keysMutex      sync.RWMutex
}

// NewKeyManagementService creates a new key management service
func NewKeyManagementService(
	keyStore ports.KeyStore,
	cache ports.Cache,
	logger logger.Interface,
	keySize int,
	keyValidity time.Duration,
	rotationPeriod time.Duration,
) *KeyManagementService {
	return &KeyManagementService{
		keyStore:       keyStore,
		cache:          cache,
		logger:         logger,
		keySize:        keySize,
		keyValidity:    keyValidity,
		rotationPeriod: rotationPeriod,
	}
}

// Initialize loads or generates the initial key pair
func (s *KeyManagementService) Initialize(ctx context.Context) error {
	s.keysMutex.Lock()
	defer s.keysMutex.Unlock()

	// Try to load existing active key
	activeKey, err := s.keyStore.GetActiveKey(ctx)
	if err != nil {
		s.logger.WithError(err).Info("No active key found, generating new key pair")

		// Generate new key pair
		keyPair, err := s.generateNewKeyPair()
		if err != nil {
			return fmt.Errorf("failed to generate initial key pair: %w", err)
		}

		// Store the new key
		if err := s.keyStore.StoreKey(ctx, keyPair); err != nil {
			return fmt.Errorf("failed to store initial key pair: %w", err)
		}

		// Set as active
		if err := s.keyStore.SetActiveKey(ctx, keyPair.ID); err != nil {
			return fmt.Errorf("failed to set active key: %w", err)
		}

		s.currentKey = keyPair
		s.logger.WithField("key_id", keyPair.ID).Info("Generated and activated new key pair")
	} else {
		// Validate existing key
		if err := activeKey.Validate(); err != nil {
			return fmt.Errorf("active key validation failed: %w", err)
		}

		s.currentKey = activeKey
		s.logger.WithField("key_id", activeKey.ID).Info("Loaded existing active key pair")
	}

	// Start key rotation routine
	go s.startKeyRotationRoutine(ctx)

	return nil
}

// GetCurrentSigningKey returns the current active key for signing
func (s *KeyManagementService) GetCurrentSigningKey() *valueobjects.JWTKeyPair {
	s.keysMutex.RLock()
	defer s.keysMutex.RUnlock()
	return s.currentKey
}

// GetKeyByID retrieves a key by its ID for token verification
func (s *KeyManagementService) GetKeyByID(ctx context.Context, keyID string) (*valueobjects.JWTKeyPair, error) {
	return s.keyStore.GetKeyByID(ctx, keyID)
}

// GetJWKS returns all valid public keys in JWKS format
func (s *KeyManagementService) GetJWKS(ctx context.Context) (map[string]interface{}, error) {
	keys, err := s.keyStore.GetValidKeys(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get valid keys: %w", err)
	}

	jwks := map[string]interface{}{
		"keys": make([]map[string]interface{}, 0, len(keys)),
	}

	for _, key := range keys {
		if !key.IsExpired() {
			jwks["keys"] = append(jwks["keys"].([]map[string]interface{}), key.ToJWK())
		}
	}

	return jwks, nil
}

// RotateKey generates a new key pair and sets it as active
func (s *KeyManagementService) RotateKey(ctx context.Context) error {
	s.keysMutex.Lock()
	defer s.keysMutex.Unlock()

	s.logger.Info("Starting key rotation")

	// Generate new key pair
	newKeyPair, err := s.generateNewKeyPair()
	if err != nil {
		return fmt.Errorf("failed to generate new key pair: %w", err)
	}

	// Store the new key
	if err := s.keyStore.StoreKey(ctx, newKeyPair); err != nil {
		return fmt.Errorf("failed to store new key pair: %w", err)
	}

	// Set as active
	if err := s.keyStore.SetActiveKey(ctx, newKeyPair.ID); err != nil {
		return fmt.Errorf("failed to set new active key: %w", err)
	}

	// Keep old key for a grace period to allow existing tokens to be validated
	if s.currentKey != nil {
		s.currentKey.IsActive = false
		if err := s.keyStore.UpdateKey(ctx, s.currentKey); err != nil {
			s.logger.WithError(err).Error("Failed to update old key status")
		}
	}

	s.currentKey = newKeyPair
	s.logger.WithField("key_id", newKeyPair.ID).Info("Key rotation completed successfully")

	// Clear JWKS cache to force refresh
	if err := s.cache.Delete(ctx, "jwks"); err != nil {
		s.logger.WithError(err).Error("Failed to clear JWKS cache")
	}

	return nil
}

// GenerateSecureRefreshToken generates a cryptographically secure random refresh token
func (s *KeyManagementService) GenerateSecureRefreshToken() (string, error) {
	// Generate 32 bytes of random data (256 bits)
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// Return as hex string
	return hex.EncodeToString(bytes), nil
}

// generateNewKeyPair creates a new RSA key pair
func (s *KeyManagementService) generateNewKeyPair() (*valueobjects.JWTKeyPair, error) {
	keyPair, err := valueobjects.NewJWTKeyPair(s.keySize, s.keyValidity)
	if err != nil {
		return nil, fmt.Errorf("failed to create key pair: %w", err)
	}

	if err := keyPair.Validate(); err != nil {
		return nil, fmt.Errorf("generated key pair validation failed: %w", err)
	}

	return keyPair, nil
}

// startKeyRotationRoutine runs the automatic key rotation
func (s *KeyManagementService) startKeyRotationRoutine(ctx context.Context) {
	ticker := time.NewTicker(s.rotationPeriod)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("Key rotation routine stopped")
			return
		case <-ticker.C:
			s.checkAndRotateKey(ctx)
		}
	}
}

// checkAndRotateKey checks if key rotation is needed and performs it
func (s *KeyManagementService) checkAndRotateKey(ctx context.Context) {
	s.keysMutex.RLock()
	currentKey := s.currentKey
	s.keysMutex.RUnlock()

	if currentKey == nil {
		s.logger.Error("No current key available for rotation check")
		return
	}

	if currentKey.ShouldRotate() || currentKey.IsExpired() {
		s.logger.WithField("key_id", currentKey.ID).Info("Key rotation needed")

		if err := s.RotateKey(ctx); err != nil {
			s.logger.WithError(err).Error("Failed to rotate key")
		}
	}

	// Clean up expired keys
	if err := s.keyStore.CleanupExpiredKeys(ctx); err != nil {
		s.logger.WithError(err).Error("Failed to cleanup expired keys")
	}
}
