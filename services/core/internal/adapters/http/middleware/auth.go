package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/golang-jwt/jwt/v5"

	httpPkg "github.com/paradoxe35/torra/packages/http"
	"github.com/paradoxe35/torra/packages/logger"
	"github.com/paradoxe35/torra/services/core/internal/application/services"
)

type AuthMiddleware struct {
	keyManager *services.KeyManagementService
	logger     logger.Interface
}

func NewAuthMiddleware(keyManager *services.KeyManagementService, logger logger.Interface) *AuthMiddleware {
	return &AuthMiddleware{
		keyManager: keyManager,
		logger:     logger,
	}
}

func (m *AuthMiddleware) Authenticate(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Extract token from Authorization header
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			httpPkg.RespondError(w, http.StatusUnauthorized, "MISSING_TOKEN", "Authorization header is required")
			return
		}

		// Check for Bearer prefix
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			httpPkg.RespondError(w, http.StatusUnauthorized, "INVALID_TOKEN_FORMAT", "Invalid authorization header format")
			return
		}

		tokenString := parts[1]

		// Parse and validate token
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			// Validate signing method - expect RS256
			if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}

			// Get key ID from token header
			kidInterface, ok := token.Header["kid"]
			if !ok {
				return nil, fmt.Errorf("missing key ID in token header")
			}

			kid, ok := kidInterface.(string)
			if !ok {
				return nil, fmt.Errorf("invalid key ID format")
			}

			// Get the public key for verification
			keyPair, err := m.keyManager.GetKeyByID(r.Context(), kid)
			if err != nil {
				return nil, fmt.Errorf("failed to get key: %w", err)
			}

			if keyPair.PublicKey == nil {
				return nil, fmt.Errorf("public key not available")
			}

			return keyPair.PublicKey, nil
		})

		if err != nil || !token.Valid {
			m.logger.WithError(err).Debug("Invalid token")
			httpPkg.RespondError(w, http.StatusUnauthorized, "INVALID_TOKEN", "Invalid or expired token")
			return
		}

		// Extract claims
		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			httpPkg.RespondError(w, http.StatusUnauthorized, "INVALID_CLAIMS", "Invalid token claims")
			return
		}

		// Add user info to context
		ctx := context.WithValue(r.Context(), "user_id", claims["sub"])
		ctx = context.WithValue(ctx, "user_email", claims["email"])
		ctx = context.WithValue(ctx, "area_id", claims["area_id"])

		// Continue with the request
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
