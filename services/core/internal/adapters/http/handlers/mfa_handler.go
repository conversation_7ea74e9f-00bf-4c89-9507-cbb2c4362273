package handlers

import (
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/google/uuid"
	httpPkg "github.com/paradoxe35/torra/packages/http"
	"github.com/paradoxe35/torra/packages/logger"
	"github.com/paradoxe35/torra/services/core/internal/application/dtos"
	"github.com/paradoxe35/torra/services/core/internal/application/services"
)

type MFAHandler struct {
	mfaService *services.MFAService
	logger     logger.Interface
}

func NewMFAHandler(mfaService *services.MFAService, logger logger.Interface) *MFAHandler {
	return &MFAHandler{
		mfaService: mfaService,
		logger:     logger,
	}
}

// SetupMFA initiates MFA setup for a user
// POST /api/v1/mfa/setup
func (h *MFAHandler) SetupMFA(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user ID from context (set by auth middleware)
	userID, ok := ctx.Value("user_id").(uuid.UUID)
	if !ok {
		httpPkg.ErrorResponse(w, http.StatusUnauthorized, "Unauthorized", nil)
		return
	}

	var req dtos.MFASetupRequest
	if err := httpPkg.DecodeJSON(r, &req); err != nil {
		httpPkg.ErrorResponse(w, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	response, err := h.mfaService.SetupMFA(ctx, userID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to setup MFA")
		httpPkg.ErrorResponse(w, http.StatusInternalServerError, "Failed to setup MFA", err)
		return
	}

	httpPkg.JSONResponse(w, http.StatusOK, response)
}

// VerifySetup verifies MFA setup with a code
// POST /api/v1/mfa/verify-setup
func (h *MFAHandler) VerifySetup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user ID from context
	userID, ok := ctx.Value("user_id").(uuid.UUID)
	if !ok {
		httpPkg.ErrorResponse(w, http.StatusUnauthorized, "Unauthorized", nil)
		return
	}

	var req dtos.MFAVerifySetupRequest
	if err := httpPkg.DecodeJSON(r, &req); err != nil {
		httpPkg.ErrorResponse(w, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	if err := h.mfaService.VerifySetup(ctx, userID, req); err != nil {
		h.logger.WithError(err).Error("Failed to verify MFA setup")
		httpPkg.ErrorResponse(w, http.StatusBadRequest, "Failed to verify MFA setup", err)
		return
	}

	httpPkg.JSONResponse(w, http.StatusOK, map[string]string{
		"message": "MFA setup completed successfully",
	})
}

// GetMFAStatus returns the MFA status for the current user
// GET /api/v1/mfa/status
func (h *MFAHandler) GetMFAStatus(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user ID from context
	userID, ok := ctx.Value("user_id").(uuid.UUID)
	if !ok {
		httpPkg.ErrorResponse(w, http.StatusUnauthorized, "Unauthorized", nil)
		return
	}

	status, err := h.mfaService.GetMFAStatus(ctx, userID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get MFA status")
		httpPkg.ErrorResponse(w, http.StatusInternalServerError, "Failed to get MFA status", err)
		return
	}

	httpPkg.JSONResponse(w, http.StatusOK, status)
}

// DisableMFA disables MFA for the current user
// POST /api/v1/mfa/disable
func (h *MFAHandler) DisableMFA(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user ID from context
	userID, ok := ctx.Value("user_id").(uuid.UUID)
	if !ok {
		httpPkg.ErrorResponse(w, http.StatusUnauthorized, "Unauthorized", nil)
		return
	}

	var req dtos.MFADisableRequest
	if err := httpPkg.DecodeJSON(r, &req); err != nil {
		httpPkg.ErrorResponse(w, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	if err := h.mfaService.DisableMFA(ctx, userID, req); err != nil {
		h.logger.WithError(err).Error("Failed to disable MFA")
		httpPkg.ErrorResponse(w, http.StatusBadRequest, "Failed to disable MFA", err)
		return
	}

	httpPkg.JSONResponse(w, http.StatusOK, map[string]string{
		"message": "MFA disabled successfully",
	})
}

// RegenerateBackupCodes generates new backup codes
// POST /api/v1/mfa/backup-codes/regenerate
func (h *MFAHandler) RegenerateBackupCodes(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user ID from context
	userID, ok := ctx.Value("user_id").(uuid.UUID)
	if !ok {
		httpPkg.ErrorResponse(w, http.StatusUnauthorized, "Unauthorized", nil)
		return
	}

	var req dtos.MFARegenerateBackupCodesRequest
	if err := httpPkg.DecodeJSON(r, &req); err != nil {
		httpPkg.ErrorResponse(w, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	response, err := h.mfaService.RegenerateBackupCodes(ctx, userID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to regenerate backup codes")
		httpPkg.ErrorResponse(w, http.StatusBadRequest, "Failed to regenerate backup codes", err)
		return
	}

	httpPkg.JSONResponse(w, http.StatusOK, response)
}

// RemoveDevice removes an MFA device
// DELETE /api/v1/mfa/devices/{deviceId}
func (h *MFAHandler) RemoveDevice(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user ID from context
	userID, ok := ctx.Value("user_id").(uuid.UUID)
	if !ok {
		httpPkg.ErrorResponse(w, http.StatusUnauthorized, "Unauthorized", nil)
		return
	}

	// Get device ID from URL
	deviceIDStr := chi.URLParam(r, "deviceId")
	deviceID, err := uuid.Parse(deviceIDStr)
	if err != nil {
		httpPkg.ErrorResponse(w, http.StatusBadRequest, "Invalid device ID", err)
		return
	}

	var req dtos.MFARemoveDeviceRequest
	if err := httpPkg.DecodeJSON(r, &req); err != nil {
		httpPkg.ErrorResponse(w, http.StatusBadRequest, "Invalid request body", err)
		return
	}
	req.DeviceID = deviceID.String()

	if err := h.mfaService.RemoveDevice(ctx, userID, req); err != nil {
		h.logger.WithError(err).Error("Failed to remove MFA device")
		httpPkg.ErrorResponse(w, http.StatusBadRequest, "Failed to remove MFA device", err)
		return
	}

	httpPkg.JSONResponse(w, http.StatusOK, map[string]string{
		"message": "MFA device removed successfully",
	})
}

// SendCode sends an MFA code via SMS or email
// POST /api/v1/mfa/send-code
func (h *MFAHandler) SendCode(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var req dtos.MFASendCodeRequest
	if err := httpPkg.DecodeJSON(r, &req); err != nil {
		httpPkg.ErrorResponse(w, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	response, err := h.mfaService.SendCode(ctx, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to send MFA code")
		httpPkg.ErrorResponse(w, http.StatusBadRequest, "Failed to send MFA code", err)
		return
	}

	httpPkg.JSONResponse(w, http.StatusOK, response)
}

// VerifyMFA verifies an MFA code during login
// POST /api/v1/mfa/verify
func (h *MFAHandler) VerifyMFA(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var req dtos.MFAVerifyRequest
	if err := httpPkg.DecodeJSON(r, &req); err != nil {
		httpPkg.ErrorResponse(w, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	response, err := h.mfaService.VerifyMFA(ctx, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to verify MFA")
		httpPkg.ErrorResponse(w, http.StatusBadRequest, "Failed to verify MFA", err)
		return
	}

	httpPkg.JSONResponse(w, http.StatusOK, response)
}

// GetSecurityLog returns MFA security logs for the current user
// GET /api/v1/mfa/security-log
func (h *MFAHandler) GetSecurityLog(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user ID from context
	userID, ok := ctx.Value("user_id").(uuid.UUID)
	if !ok {
		httpPkg.ErrorResponse(w, http.StatusUnauthorized, "Unauthorized", nil)
		return
	}

	logs, err := h.mfaService.GetSecurityLog(ctx, userID, 50) // Last 50 attempts
	if err != nil {
		h.logger.WithError(err).Error("Failed to get MFA security log")
		httpPkg.ErrorResponse(w, http.StatusInternalServerError, "Failed to get security log", err)
		return
	}

	httpPkg.JSONResponse(w, http.StatusOK, logs)
}
