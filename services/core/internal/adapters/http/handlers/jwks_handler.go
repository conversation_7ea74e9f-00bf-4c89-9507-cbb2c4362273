package handlers

import (
	"net/http"
	"time"

	httpPkg "github.com/paradoxe35/torra/packages/http"
	"github.com/paradoxe35/torra/packages/logger"
	"github.com/paradoxe35/torra/services/core/internal/application/ports"
	"github.com/paradoxe35/torra/services/core/internal/application/services"
)

// JWKSHandler handles JSON Web Key Set endpoints
type JW<PERSON><PERSON>andler struct {
	keyManager *services.KeyManagementService
	cache      ports.Cache
	logger     logger.Interface
}

// NewJWKSHandler creates a new JWKS handler
func NewJWKSHandler(
	keyManager *services.KeyManagementService,
	cache ports.Cache,
	logger logger.Interface,
) *JWKSHandler {
	return &JWKSHandler{
		keyManager: keyManager,
		cache:      cache,
		logger:     logger,
	}
}

// GetJWKS handles GET /.well-known/jwks.json
func (h *JWKSHandler) GetJWKS(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Try to get <PERSON><PERSON><PERSON> from cache first
	cacheKey := "jwks"
	if cachedJWKS, err := h.cache.Get(ctx, cacheKey); err == nil {
		h.logger.Debug("Serving JWKS from cache")
		
		// Set appropriate headers for JWKS
		w.Header().Set("Content-Type", "application/json")
		w.Header().Set("Cache-Control", "public, max-age=3600") // Cache for 1 hour
		w.Header().Set("Access-Control-Allow-Origin", "*")     // Allow CORS for public keys
		w.Header().Set("Access-Control-Allow-Methods", "GET")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
		
		w.WriteHeader(http.StatusOK)
		w.Write(cachedJWKS)
		return
	}

	// Get JWKS from key management service
	jwks, err := h.keyManager.GetJWKS(ctx)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get JWKS")
		httpPkg.RespondError(w, http.StatusInternalServerError, "JWKS_ERROR", "Failed to retrieve public keys")
		return
	}

	// Cache the JWKS for 1 hour
	if jwksBytes, err := httpPkg.MarshalJSON(jwks); err == nil {
		if err := h.cache.Set(ctx, cacheKey, jwksBytes, time.Hour); err != nil {
			h.logger.WithError(err).Error("Failed to cache JWKS")
		}
	}

	// Set appropriate headers for JWKS
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Cache-Control", "public, max-age=3600") // Cache for 1 hour
	w.Header().Set("Access-Control-Allow-Origin", "*")     // Allow CORS for public keys
	w.Header().Set("Access-Control-Allow-Methods", "GET")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	// Log JWKS access for monitoring
	h.logger.WithField("keys_count", len(jwks["keys"].([]map[string]interface{}))).Info("JWKS accessed")

	httpPkg.RespondJSON(w, http.StatusOK, jwks)
}

// GetOpenIDConfiguration handles GET /.well-known/openid_configuration
// This provides OpenID Connect discovery information
func (h *JWKSHandler) GetOpenIDConfiguration(w http.ResponseWriter, r *http.Request) {
	// Get the base URL from the request
	scheme := "https"
	if r.TLS == nil {
		scheme = "http"
	}
	baseURL := scheme + "://" + r.Host

	config := map[string]interface{}{
		"issuer":                 baseURL,
		"jwks_uri":              baseURL + "/.well-known/jwks.json",
		"authorization_endpoint": baseURL + "/api/v1/auth/authorize",
		"token_endpoint":        baseURL + "/api/v1/auth/token",
		"userinfo_endpoint":     baseURL + "/api/v1/auth/userinfo",
		"response_types_supported": []string{
			"code",
			"token",
			"id_token",
			"code token",
			"code id_token",
			"token id_token",
			"code token id_token",
		},
		"subject_types_supported": []string{"public"},
		"id_token_signing_alg_values_supported": []string{"RS256"},
		"scopes_supported": []string{
			"openid",
			"profile",
			"email",
		},
		"token_endpoint_auth_methods_supported": []string{
			"client_secret_basic",
			"client_secret_post",
		},
		"claims_supported": []string{
			"sub",
			"iss",
			"aud",
			"exp",
			"iat",
			"email",
			"name",
			"area_id",
		},
	}

	// Set appropriate headers
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Cache-Control", "public, max-age=86400") // Cache for 24 hours
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	httpPkg.RespondJSON(w, http.StatusOK, config)
}

// Health check for JWKS service
func (h *JWKSHandler) Health(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Check if we can get JWKS
	_, err := h.keyManager.GetJWKS(ctx)
	if err != nil {
		h.logger.WithError(err).Error("JWKS health check failed")
		httpPkg.RespondError(w, http.StatusServiceUnavailable, "JWKS_UNHEALTHY", "JWKS service is not available")
		return
	}

	// Check if we have an active signing key
	signingKey := h.keyManager.GetCurrentSigningKey()
	if signingKey == nil {
		h.logger.Error("No active signing key available")
		httpPkg.RespondError(w, http.StatusServiceUnavailable, "NO_SIGNING_KEY", "No active signing key available")
		return
	}

	response := map[string]interface{}{
		"status":           "healthy",
		"active_key_id":    signingKey.ID,
		"key_expires_at":   signingKey.ExpiresAt,
		"should_rotate":    signingKey.ShouldRotate(),
		"service":          "jwks",
	}

	httpPkg.RespondJSON(w, http.StatusOK, response)
}
