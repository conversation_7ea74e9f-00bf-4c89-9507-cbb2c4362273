package repositories

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/paradoxe35/torra/services/core/internal/domain/valueobjects"
)

// PostgresKeyStore implements the KeyStore interface using PostgreSQL
type PostgresKeyStore struct {
	db *sqlx.DB
}

// NewPostgresKeyStore creates a new PostgreSQL key store
func NewPostgresKeyStore(db *sqlx.DB) *PostgresKeyStore {
	return &PostgresKeyStore{db: db}
}

// keyRecord represents the database record for JWT keys
type keyRecord struct {
	ID         string    `db:"id"`
	Algorithm  string    `db:"algorithm"`
	Use        string    `db:"use_type"`
	KeyType    string    `db:"key_type"`
	PrivateKey []byte    `db:"private_key_pem"`
	N          string    `db:"n"`
	E          string    `db:"e"`
	CreatedAt  time.Time `db:"created_at"`
	ExpiresAt  time.Time `db:"expires_at"`
	IsActive   bool      `db:"is_active"`
}

// StoreKey stores a new JWT key pair
func (r *PostgresKeyStore) StoreKey(ctx context.Context, key *valueobjects.JWTKeyPair) error {
	// Convert private key to PEM format
	privateKeyPEM, err := key.ToPEM()
	if err != nil {
		return fmt.Errorf("failed to convert private key to PEM: %w", err)
	}

	query := `
		INSERT INTO jwt_keys (
			id, algorithm, use_type, key_type, private_key_pem, 
			n, e, created_at, expires_at, is_active
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10
		)`

	_, err = r.db.ExecContext(ctx, query,
		key.ID, key.Algorithm, key.Use, key.KeyType, privateKeyPEM,
		key.N, key.E, key.CreatedAt, key.ExpiresAt, key.IsActive,
	)

	if err != nil {
		return fmt.Errorf("failed to store JWT key: %w", err)
	}

	return nil
}

// GetActiveKey retrieves the currently active key
func (r *PostgresKeyStore) GetActiveKey(ctx context.Context) (*valueobjects.JWTKeyPair, error) {
	query := `
		SELECT id, algorithm, use_type, key_type, private_key_pem, 
			   n, e, created_at, expires_at, is_active
		FROM jwt_keys 
		WHERE is_active = true AND expires_at > NOW()
		ORDER BY created_at DESC
		LIMIT 1`

	var record keyRecord
	err := r.db.GetContext(ctx, &record, query)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("no active key found")
		}
		return nil, fmt.Errorf("failed to get active key: %w", err)
	}

	return r.recordToKeyPair(&record)
}

// GetKeyByID retrieves a key by its ID
func (r *PostgresKeyStore) GetKeyByID(ctx context.Context, keyID string) (*valueobjects.JWTKeyPair, error) {
	query := `
		SELECT id, algorithm, use_type, key_type, private_key_pem, 
			   n, e, created_at, expires_at, is_active
		FROM jwt_keys 
		WHERE id = $1`

	var record keyRecord
	err := r.db.GetContext(ctx, &record, query, keyID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("key not found: %s", keyID)
		}
		return nil, fmt.Errorf("failed to get key by ID: %w", err)
	}

	return r.recordToKeyPair(&record)
}

// GetValidKeys retrieves all valid (non-expired) keys
func (r *PostgresKeyStore) GetValidKeys(ctx context.Context) ([]*valueobjects.JWTKeyPair, error) {
	query := `
		SELECT id, algorithm, use_type, key_type, private_key_pem, 
			   n, e, created_at, expires_at, is_active
		FROM jwt_keys 
		WHERE expires_at > NOW()
		ORDER BY created_at DESC`

	var records []keyRecord
	err := r.db.SelectContext(ctx, &records, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get valid keys: %w", err)
	}

	keys := make([]*valueobjects.JWTKeyPair, 0, len(records))
	for _, record := range records {
		key, err := r.recordToKeyPair(&record)
		if err != nil {
			// Log error but continue with other keys
			continue
		}
		keys = append(keys, key)
	}

	return keys, nil
}

// SetActiveKey sets a key as the active one and deactivates others
func (r *PostgresKeyStore) SetActiveKey(ctx context.Context, keyID string) error {
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Deactivate all keys
	_, err = tx.ExecContext(ctx, "UPDATE jwt_keys SET is_active = false")
	if err != nil {
		return database.HandleError(err, "failed to deactivate keys")
	}

	// Activate the specified key
	result, err := tx.ExecContext(ctx,
		"UPDATE jwt_keys SET is_active = true WHERE id = $1", keyID)
	if err != nil {
		return database.HandleError(err, "failed to activate key")
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("key not found: %s", keyID)
	}

	return tx.Commit()
}

// UpdateKey updates an existing key
func (r *PostgresKeyStore) UpdateKey(ctx context.Context, key *valueobjects.JWTKeyPair) error {
	query := `
		UPDATE jwt_keys 
		SET is_active = $2, expires_at = $3
		WHERE id = $1`

	result, err := r.db.ExecContext(ctx, query, key.ID, key.IsActive, key.ExpiresAt)
	if err != nil {
		return database.HandleError(err, "failed to update key")
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("key not found: %s", key.ID)
	}

	return nil
}

// CleanupExpiredKeys removes expired keys from the database
func (r *PostgresKeyStore) CleanupExpiredKeys(ctx context.Context) error {
	// Keep expired keys for a grace period (e.g., 24 hours) to allow existing tokens to be validated
	gracePeriod := 24 * time.Hour
	cutoffTime := time.Now().Add(-gracePeriod)

	query := `DELETE FROM jwt_keys WHERE expires_at < $1`

	result, err := r.db.ExecContext(ctx, query, cutoffTime)
	if err != nil {
		return database.HandleError(err, "failed to cleanup expired keys")
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected > 0 {
		// Log the cleanup operation
		fmt.Printf("Cleaned up %d expired JWT keys\n", rowsAffected)
	}

	return nil
}

// recordToKeyPair converts a database record to a JWTKeyPair
func (r *PostgresKeyStore) recordToKeyPair(record *keyRecord) (*valueobjects.JWTKeyPair, error) {
	keyPair := &valueobjects.JWTKeyPair{
		ID:        record.ID,
		Algorithm: record.Algorithm,
		Use:       record.Use,
		KeyType:   record.KeyType,
		N:         record.N,
		E:         record.E,
		CreatedAt: record.CreatedAt,
		ExpiresAt: record.ExpiresAt,
		IsActive:  record.IsActive,
	}

	// Load private key from PEM
	if err := keyPair.FromPEM(record.PrivateKey); err != nil {
		return nil, fmt.Errorf("failed to load private key from PEM: %w", err)
	}

	return keyPair, nil
}
